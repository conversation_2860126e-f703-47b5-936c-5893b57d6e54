package pt.jumia.services.brad.domain.usecases.bale.brad;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import pt.jumia.services.brad.domain.entities.Bale;
import pt.jumia.services.brad.domain.entities.Currency;
import pt.jumia.services.brad.domain.entities.FxRate;
import pt.jumia.services.brad.domain.entities.account.Account;
import pt.jumia.services.brad.domain.exceptions.DatabaseErrorsException;
import pt.jumia.services.brad.domain.repository.brad.BradBaleRepository;
import pt.jumia.services.brad.domain.usecases.accounts.ReadAccountsUseCase;
import pt.jumia.services.brad.domain.usecases.currencies.ReadCurrenciesUseCase;
import pt.jumia.services.brad.domain.usecases.fxrates.brad.ReadBradFxRateUseCase;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Slf4j
@AllArgsConstructor
@Component
public class SyncBradBaleUseCase {

    private final BradBaleRepository bradBaleRepository;
    private final ReadCurrenciesUseCase readCurrenciesUseCase;
    private final ReadAccountsUseCase readAccountsUseCase;
    private final ReadBradFxRateUseCase readBradFxRateUseCase;

    private static final String USD = "USD";

    public List<Bale> execute(List<Bale> baleList) throws DatabaseErrorsException {
        if (baleList == null || baleList.isEmpty()) {
            log.debug("No bales to sync");
            return List.of();
        }

        log.info("Syncing {} bales to Brad database", baleList.size());

        try {
            List<Bale> syncedBales = bradBaleRepository.sync(baleList);

            log.info("Successfully synced {} bales to Brad database", syncedBales.size());
            return syncedBales;

        } catch (Exception e) {
            log.error("Failed to sync {} bales to Brad database", baleList.size(), e);
            throw new DatabaseErrorsException("Bale sync failed: " + e.getMessage());
        }
    }

    public Bale processBale(Bale bale) throws Exception {
        if (bale == null) {
            throw new IllegalArgumentException("Bale cannot be null");
        }

        log.debug("Processing bale with entry number: {}", bale.getEntryNo());

        validateBale(bale);

        Account account = fetchAccount(bale);

        Currency currency = determineCurrency(bale, account);

        Set<FxRate> fxRates = enrichWithFxRates(bale, currency);

        Bale enrichedBale = bale.toBuilder()
                .account(account)
                .transactionCurrency(currency)
                .fxRates(fxRates)
                .build();

        log.debug("Successfully processed bale with entry number: {}", bale.getEntryNo());
        return enrichedBale;
    }

    private Set<FxRate> enrichWithFxRates(Bale bale, Currency transactionCurrency) {
        Set<FxRate> fxRates = new HashSet<>();
        String baleCurrencyCode = transactionCurrency.getCode();

        this.readBradFxRateUseCase.addFxRate(fxRates, baleCurrencyCode, USD, bale.getPostingDate());

        if (bale.getAccount() != null &&
            bale.getAccount().getCountry() != null && 
            bale.getAccount().getCountry().getCurrency() != null) {
            this.readBradFxRateUseCase.addFxRate(fxRates, baleCurrencyCode, 
                bale.getAccount().getCountry().getCurrency().getCode(), bale.getPostingDate());
        }

        return fxRates;
    }

    private void validateBale(Bale bale) throws Exception {
        if (bale.getAccount() == null || bale.getAccount().getNavReference() == null ||
            bale.getAccount().getNavReference().trim().isEmpty()) {
            throw new IllegalArgumentException("Bale account NAV reference cannot be null or empty");
        }

        if (bale.getEntryNo() == null) {
            throw new IllegalArgumentException("Bale entry number cannot be null");
        }
    }

    private Account fetchAccount(Bale bale) throws Exception {
        try {
            String navReference = bale.getAccount().getNavReference();
            return readAccountsUseCase.executeByNavReference(navReference);
        } catch (Exception e) {
            String navRef = bale.getAccount() != null ? bale.getAccount().getNavReference() : "null";
            throw new Exception("Failed to fetch account for NAV reference: " + navRef, e);
        }
    }

    private Currency determineCurrency(Bale bale, Account account) throws Exception {
        try {
            if (account.getCurrency() != null && account.getCurrency().getId() != null) {
                return readCurrenciesUseCase.execute(account.getCurrency().getId());
            } else {
                return readCurrenciesUseCase.execute(USD);
            }
        } catch (Exception e) {
            throw new Exception("Failed to determine currency for bale: " + bale.getEntryNo(), e);
        }
    }

    public Bale addFxRates(Bale bale) throws Exception {
        if (bale == null) {
            throw new IllegalArgumentException("Bale cannot be null");
        }

        if (bale.getAccount() == null) {
            throw new IllegalArgumentException("Bale account cannot be null");
        }

        Currency transactionCurrency = bale.getTransactionCurrency();
        if (transactionCurrency == null) {
            throw new IllegalArgumentException("Bale transaction currency cannot be null");
        }

        Set<FxRate> fxRates = enrichWithFxRates(bale, transactionCurrency);

        return bale.toBuilder()
                .fxRates(fxRates)
                .build();
    }
}
