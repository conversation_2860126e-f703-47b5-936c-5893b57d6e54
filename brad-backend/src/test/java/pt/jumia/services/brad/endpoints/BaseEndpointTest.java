package pt.jumia.services.brad.endpoints;

import com.fasterxml.jackson.databind.ObjectMapper;
import java.time.LocalDate;
import lombok.SneakyThrows;
import org.awaitility.Awaitility;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import pt.jumia.services.acl.lib.AclErrorException;
import pt.jumia.services.acl.lib.RequestUser;
import pt.jumia.services.brad.api.payloads.response.*;
import pt.jumia.services.brad.domain.Permissions;
import pt.jumia.services.brad.domain.Profiles;
import pt.jumia.services.brad.domain.entities.Currency;
import pt.jumia.services.brad.domain.entities.*;
import pt.jumia.services.brad.domain.entities.account.User;
import pt.jumia.services.brad.domain.entities.account.*;
import pt.jumia.services.brad.domain.entities.filter.accountstatement.AccountStatementFilters;
import pt.jumia.services.brad.domain.entities.filter.transaction.TransactionFilters;
import pt.jumia.services.brad.domain.entities.reconciliation.Reconciliation;
import pt.jumia.services.brad.domain.entities.reconciliation.Threshold;
import pt.jumia.services.brad.domain.enumerations.AccountStatementStatus;
import pt.jumia.services.brad.domain.exceptions.DatabaseErrorsException;
import pt.jumia.services.brad.domain.exceptions.NotFoundException;
import pt.jumia.services.brad.domain.repository.brad.*;
import pt.jumia.services.brad.domain.usecases.bale.brad.SyncBradBaleUseCase;
import pt.jumia.services.brad.domain.usecases.accountstatement.SyncBradFinrecStatementsUseCase;
import pt.jumia.services.brad.domain.usecases.fxrates.brad.SyncBradFxRateUseCase;
import pt.jumia.services.brad.network.acl.AclNetworkRequester;
import pt.jumia.services.brad.robots.*;
import pt.jumia.services.brad.robots.audit.AccountAuditRobot;

import java.text.ParseException;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

@ExtendWith(SpringExtension.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles({Profiles.FAKE_CLIENTS, Profiles.TEST})
public abstract class BaseEndpointTest {

    @LocalServerPort
    protected int port;

    @MockBean
    private AclNetworkRequester aclNetworkRequester;
    @Autowired
    protected ObjectMapper objectMapper;

    private final Stack<Long> accountsToDelete = new Stack<>();
    private final Stack<Long> contactsToDelete = new Stack<>();
    private final Stack<Long> usersToDelete = new Stack<>();
    private final Stack<Long> documentsToDelete = new Stack<>();
    private final Stack<Long> accountStatementsAccountIDsToDelete = new Stack<>();
    private final Stack<Long> balesToDelete = new Stack<>();
    private final Stack<Integer> fxRatesToDelete = new Stack<>();
    protected final Stack<Integer> reconciliationsToDelete = new Stack<>();
    private final Stack<Long> countriesToDelete = new Stack<>();
    private final Stack<Long> apiLogsToDelete = new Stack<>();
    private final Stack<Long> currenciesToDelete = new Stack<>();
    private final Stack<Long> executionLogsToDelete = new Stack<>();
    private final Stack<Long> baleViewEntityToDelete = new Stack<>();
    private final Stack<Long> thresholdToDelete = new Stack<>();
    private final Stack<Long> fxRateViewEntityToDelete = new Stack<>();


    protected SpringActuatorRobot springActuatorRobot;
    protected AccountsRobot accountsRobot;
    protected UsersRobot usersRobot;
    protected ContactsRobot contactsRobot;
    protected AccountAuditRobot accountAuditRobot;
    protected DocumentsRobot documentsRobot;
    protected AccountStatementRobot accountStatementRobot;
    protected BaleRobot baleRobot;
    protected FxRateRobot fxRateRobot;
    protected ReconciliationRobot reconciliationRobot;
    protected CountryRobot countryRobot;
    protected TransactionRobot transactionRobot;
    protected ApiLogRobot apiLogRobot;
    protected ExecutionLogRobot executionLogRobot;
    protected CurrencyRobot currencyRobot;
    protected BaleViewEntityRobot baleViewEntityRobot;
    protected FxRateViewEntityRobot fxRateViewEntityRobot;
    protected ThresholdRobot thresholdRobot;

    @Autowired
    protected AccountRepository accountRepository;

    @Autowired
    protected DocumentRepository documentRepository;

    @Autowired
    protected UserRepository userRepository;

    @Autowired
    protected AccountStatementRepository accountStatementRepository;

    @Autowired
    protected BradBaleRepository bradBaleRepository;

    @Autowired
    protected BradFxRateRepository bradFxRateRepository;

    @Autowired
    protected ReconciliationRepository reconciliationRepository;

    @Autowired
    protected CountryRepository countryRepository;

    @Autowired
    protected TransactionRepository transactionRepository;

    @Autowired
    protected ApiLogRepository apiLogRepository;

    @Autowired
    protected CurrencyRepository currencyRepository;

    @Autowired
    protected ExecutionLogRepository executionLogRepository;

    @Autowired
    protected ViewEntityRepository viewEntityRepository;

    @Autowired
    protected ThresholdRepository thresholdRepository;


    @Autowired
    protected SyncBradBaleUseCase syncBradBaleUseCase;

    @Autowired
    protected SyncBradFinrecStatementsUseCase syncBradFinrecStatementsUseCase;

    @Autowired
    protected SyncBradFxRateUseCase syncBradFxRateUseCase;

    @Autowired
    protected AccountDailySummaryRepository accountDailySummaryRepository;


    @BeforeEach
    public void baseSetUp() {
        springActuatorRobot = new SpringActuatorRobot(port);
        accountsRobot = new AccountsRobot(port, objectMapper);
        contactsRobot = new ContactsRobot(port, objectMapper);
        usersRobot = new UsersRobot(port, objectMapper);
        accountAuditRobot = new AccountAuditRobot(port, objectMapper);
        documentsRobot = new DocumentsRobot(port, objectMapper);
        accountStatementRobot = new AccountStatementRobot(port,objectMapper);
        baleRobot = new BaleRobot(port, objectMapper);
        fxRateRobot = new FxRateRobot(port, objectMapper);
        reconciliationRobot = new ReconciliationRobot(port, objectMapper);
        countryRobot = new CountryRobot(port, objectMapper);
        transactionRobot = new TransactionRobot(port, objectMapper);
        apiLogRobot = new ApiLogRobot(port, objectMapper);
        currencyRobot = new CurrencyRobot(port, objectMapper);
        executionLogRobot = new ExecutionLogRobot(port, objectMapper);
        baleViewEntityRobot = new BaleViewEntityRobot(port, objectMapper);
        thresholdRobot = new ThresholdRobot(port, objectMapper);
        fxRateViewEntityRobot = new FxRateViewEntityRobot(port, objectMapper);

        executionLogRepository.deleteAll();
        apiLogRepository.deleteAll();
    }

    @AfterEach
    public void baseTearDown() {
        loginUser("<EMAIL>");

        while (!baleViewEntityToDelete.empty()){
            viewEntityRepository.deleteById(baleViewEntityToDelete.pop());
        }
        while (!fxRateViewEntityToDelete.empty()){
            viewEntityRepository.deleteById(fxRateViewEntityToDelete.pop());
        }
        while (!reconciliationsToDelete.empty()){
            reconciliationRepository.delete(reconciliationsToDelete.pop());
        }
        while (!contactsToDelete.empty()){
            contactsRobot.delete(contactsToDelete.pop());
        }
        while (!usersToDelete.empty()){
            userRepository.deleteById(usersToDelete.pop());
        }
        while (!documentsToDelete.empty()){
            documentRepository.deleteById(documentsToDelete.pop());
        }
        getBalesToDelete();
        while (!balesToDelete.empty()){
            bradBaleRepository.deleteById(balesToDelete.pop());
        }
        this.deleteAllFxRatesTransactions();
        this.deleteAllFxRatesAccountStatement();
        this.deleteAllFxRatesBales();

        //transaction are now deleted here too
        while (!accountStatementsAccountIDsToDelete.empty()) {
            Long accountId = accountStatementsAccountIDsToDelete.pop();
            this.deleteAccountStatementsByAccountID(accountId);
        }
        while (!fxRatesToDelete.empty()){
            bradFxRateRepository.deleteById(fxRatesToDelete.pop());
        }
        while (!accountsToDelete.empty()){
            Long accountToDelete = accountsToDelete.pop();
            accountDailySummaryRepository.deleteByDates(accountToDelete, LocalDate.now().minusYears(1000), LocalDate.now().plusYears(1000));
            accountRepository.deleteById(accountToDelete);

        }
        while (!thresholdToDelete.empty()){
            thresholdRepository.deleteById(thresholdToDelete.pop());
        }
        while (!countriesToDelete.empty()){
            countryRepository.deleteById(countriesToDelete.pop());
        }
        getApiLogsToDelete();
        while (!apiLogsToDelete.empty()){
            apiLogRepository.deleteById(apiLogsToDelete.pop());
        }
        while (!currenciesToDelete.empty()){
            currencyRepository.deleteById(currenciesToDelete.pop());
        }
        while (!executionLogsToDelete.empty()){
            executionLogRepository.deleteById(executionLogsToDelete.pop());
        }

    }

    @SneakyThrows
    private void getApiLogsToDelete(){
        List<ApiLog> apiLogs = apiLogRepository.findAll(null, null, null);
        for (ApiLog apiLog : apiLogs) {
            apiLogsToDelete.push(apiLog.getId());
        }
    }

    @SneakyThrows
    private void getBalesToDelete() {
        List<Bale> bales = bradBaleRepository.findAll(null, null, null);
        for (Bale bale : bales) {
            balesToDelete.push(bale.getId());
        }
    }

    protected UserApiResponsePayload createUser(User user) {
        UserApiResponsePayload createdUser = usersRobot.create(user);
        usersToDelete.push(createdUser.getId());
        return createdUser;
    }

    protected Account createAccount(Account account){
        Account createdAccount = accountsRobot.create(account);
        accountsToDelete.push(createdAccount.getId());
        accountStatementsAccountIDsToDelete.push(createdAccount.getId());
        return createdAccount;
    }


    protected ContactApiResponsePayload createContact(Contact contact) {
        ContactApiResponsePayload createdContact = contactsRobot.create(contact);
        contactsToDelete.push(createdContact.getId());
        return createdContact;
    }


    protected DocumentApiResponsePayload createDocument(Document document) {
        DocumentApiResponsePayload createdDocument = documentsRobot.create(document);
        documentsToDelete.push(createdDocument.getId());
        return createdDocument;
    }


    protected List<Bale> insertBales(List<Bale> baleList) throws DatabaseErrorsException {
        List<Bale> createdBales = syncBradBaleUseCase.execute(baleList);
        for (Bale bale : createdBales) {
            balesToDelete.push(bale.getId());
        }
        return createdBales;
    }


    protected List<FxRate> insertFxRates(List<FxRate> fxRateList) throws DatabaseErrorsException, NotFoundException {
        List<FxRate> createdFxRate = syncBradFxRateUseCase.execute(fxRateList, ExecutionLog.builder().build());
        for (FxRate fxRate : createdFxRate) {
            fxRatesToDelete.push(fxRate.getId());
        }
        return createdFxRate;
    }

    protected Reconciliation insertReconciliations(Reconciliation reconciliation, Long accountId) {
        Reconciliation createdReconciliation = reconciliationRobot.reconcile(reconciliation, accountId);
        reconciliationsToDelete.push(createdReconciliation.getId());
        return createdReconciliation;
    }

    protected ThresholdApiResponsePayload insertThreshold(Threshold threshold) {
        ThresholdApiResponsePayload createdThreshold = thresholdRobot.create(threshold);
        thresholdToDelete.push(createdThreshold.getId());
        return createdThreshold;
    }

    protected CountryApiResponsePayload createCountry(Country country) {
        CountryApiResponsePayload createdCountry = countryRobot.create(country);
        countriesToDelete.push(createdCountry.getId());
        return createdCountry;
    }

    protected CurrencyApiResponsePayload createCurrency(Currency currency) {
        CurrencyApiResponsePayload createdCurrency = currencyRobot.create(currency);
        currenciesToDelete.push(createdCurrency.getId());
        return createdCurrency;
    }

    protected void insertStatementsViaBi(List<FinrecStatement> finrecStatements) throws ParseException {
        syncBradFinrecStatementsUseCase.execute(finrecStatements, ExecutionLog.builder().build());
    }

    protected void deleteAllFxRatesTransactions() {
        transactionRepository.deleteAllFxRatesTransactions();
    }

    protected void deleteAllFxRatesAccountStatement() {
        accountStatementRepository.deleteAllFxRatesAccountStatement();
    }

    protected void deleteAllFxRatesBales() {
        bradBaleRepository.deleteAllFxRatesBales();
    }

    @SneakyThrows
    protected AccountStatement createStatementWithTransactions(Account account, AccountStatement accountStatement, List<Transaction> transactions) {

        AccountStatementApiResponsePayload createdAccountStatement = accountStatementRobot.create(accountStatement.toBuilder()
                .account(account)
                .build(), transactions);
        TransactionFilters transactionFilters = TransactionFilters.builder()
                .partitionKey(String.valueOf(account.getId()))
                .accountId(account.getAccountNumber())
                .accountStatementID(String.valueOf(createdAccountStatement.getId()))
                .build();
        Awaitility.await().atMost(10, TimeUnit.SECONDS).until(() -> {
            List<Transaction> createdTransactions = transactionRepository.findAllWithReconciliation(transactionFilters, null, null);
            return createdTransactions.size() == transactions.size();
        });

        try {
            Awaitility.await().atMost(1, TimeUnit.SECONDS).until(() -> {
                Optional<AccountDailySummary> createdSummary = accountDailySummaryRepository.findLatestByAccountIdAndDate(
                    account.getId(), createdAccountStatement.getFinalDate());
                return createdSummary.isPresent();
            });
        } catch (Exception e) {
            //Continue gracefully when there is no summary created
        }

        return createdAccountStatement.toEntity(account);
    }

    @SneakyThrows
    protected AccountStatement createStatementWithTransactionsWithErrors(Account account, AccountStatement accountStatement, List<Transaction> transactions,
                                                                          int successfulTransactions) {

        AccountStatementApiResponsePayload createdAccountStatement = accountStatementRobot.create(accountStatement.toBuilder()
                .account(account)
                .build(), transactions);
        TransactionFilters transactionFilters = TransactionFilters.builder()
                .accountStatementID(String.valueOf(createdAccountStatement.getId()))
                .build();
        Awaitility.await().atMost(10, TimeUnit.SECONDS).until(() -> {
            List<Transaction> createdTransactions = transactionRepository.findAllWithReconciliation(transactionFilters, null, null);
            return createdTransactions.size() == successfulTransactions;
        });

        return createdAccountStatement.toEntity(account);
    }

    protected void createAccountStatementFailure(Account account, AccountStatement accountStatement) {

        accountStatementRobot.createNotFoundFailure(accountStatement.toBuilder()
                .account(account)
                .build());
    }

    protected List<AccountStatement> getOrderedListOfAccountStatements(AccountStatementFilters accountStatementFilters) {
        return accountStatementRepository.findAllStatementsOrdered(accountStatementFilters);
    }

    private void deleteAccountStatementsByAccountID(Long accountId) {
        AccountStatementFilters accountStatementFilters = AccountStatementFilters.builder()
                .status(AccountStatementStatus.getValues())
                .accountID(accountId).build();
        List<AccountStatement> accountStatements = getOrderedListOfAccountStatements(accountStatementFilters);

        Collections.reverse(accountStatements);
        for (AccountStatement accountStatement : accountStatements) {
            accountDailySummaryRepository.deleteByDates(accountStatement.getAccount().getId(), accountStatement.getInitialDate(),
                accountStatement.getFinalDate());
            transactionRepository.deleteByAccountStatementId(accountStatement.getId());
            accountStatementRepository.deleteById(accountStatement.getId());
        }
    }

    protected ExecutionLog createExecutionLog(ExecutionLog executionLog) {
        ExecutionLog createdExecutionLog = executionLogRepository.upsert(executionLog);
        executionLogsToDelete.push(createdExecutionLog.getId());
        return createdExecutionLog;
    }

    protected ViewEntity createBaleViewEntity(ViewEntity baleViewEntity) {
        ViewEntity createdBaleViewEntity = baleViewEntityRobot.create(baleViewEntity).toEntity();
        baleViewEntityToDelete.push(createdBaleViewEntity.getId());
        return createdBaleViewEntity;
    }

    protected ViewEntity createFxRateViewEntity(ViewEntity fxRateViewEntity) {
        ViewEntity createdBaleViewEntity = fxRateViewEntityRobot.create(fxRateViewEntity).toEntity();
        fxRateViewEntityToDelete.push(createdBaleViewEntity.getId());
        return createdBaleViewEntity;
    }

    protected void loginUser(String username) {
        mockJWT(username);
    }

    protected void anonymousUser() {
        mockJWT(null);
    }

    private void mockJWT(String username) {
        if (username == null) {
            when(aclNetworkRequester.decodeToken(eq(RestApiRobot.TOKEN))).thenThrow(AclErrorException.build(401));
            return;
        }

        Date expirationTime = new Date(new Date().getTime() + TimeUnit.HOURS.toMillis(12));
        RequestUser requestUser = RequestUser.builder()
                .exp(expirationTime.getTime())
                .username(username)
                .build();

        when(aclNetworkRequester.hasPermission(any(), any())).thenReturn(true);

        when(aclNetworkRequester.decodeToken(eq(RestApiRobot.TOKEN))).thenAnswer(invocation -> requestUser);
        when(aclNetworkRequester.authorize(username, "password")).thenAnswer(invocation -> requestUser);

        when(aclNetworkRequester.getPermissions(requestUser)).thenReturn(
                Map.of(
                        "APPLICATION", Map.of(
                                "BRAD", List.of(
                                        Permissions.BRAD_CAN_ACCESS,
                                        Permissions.BRAD_ACCESS_API_LOG,
                                        Permissions.BRAD_ADMIN_MANAGE_COUNTRIES,
                                        Permissions.BRAD_ACCESS_RECONCILIATION,
                                        Permissions.BRAD_ACCESS_SCHEDULER,
                                        Permissions.BRAD_MANAGE_SCHEDULER,
                                        Permissions.BRAD_ACCESS_CURRENCIES,
                                        Permissions.BRAD_ADMIN_MANAGE_CURRENCIES,
                                        Permissions.BRAD_ACCESS_FX_RATES,
                                        Permissions.BRAD_ACCESS_EXECUTION_LOG,
                                        Permissions.BRAD_MANAGE_VIEW_ENTITIES,
                                        Permissions.BRAD_MANAGE_THRESHOLDS
                                )
                        ),
                                "COUNTRY", Map.of(
                                        "NG", List.of(
                                                Permissions.BRAD_ACCESS_COUNTRIES,
                                                Permissions.BRAD_ACCESS_ACCOUNTS,
                                                Permissions.BRAD_EXPORT_STATEMENTS,
                                                Permissions.BRAD_DISCARD_STATEMENTS,
                                                Permissions.BRAD_RETRY_STATEMENT,
                                                Permissions.BRAD_ACCESS_TROUBLESHOOTING,
                                                Permissions.BRAD_APPROVE_RECONCILIATIONS,
                                                Permissions.BRAD_UNMATCH_RECONCILIATIONS,
                                                Permissions.BRAD_EXPORT_RECONCILIATIONS,
                                                Permissions.BRAD_UPLOAD_STATEMENTS,
                                                Permissions.BRAD_MANAGE_RECONCILIATION,
                                                Permissions.BRAD_ACCESS_STATEMENTS,
                                                Permissions.BRAD_DELETE_ACCOUNTS,
                                                Permissions.BRAD_DOWNLOAD_ACCOUNTS_CSV,
                                                Permissions.BRAD_MANAGE_ACCOUNTS,
                                                Permissions.BRAD_DOWNLOAD_CONTACTS_CSV,
                                                Permissions.BRAD_DOWNLOAD_USERS_CSV
                                ),
                                "EG", List.of(
                                        Permissions.BRAD_ACCESS_COUNTRIES,
                                        Permissions.BRAD_ACCESS_ACCOUNTS,
                                        Permissions.BRAD_EXPORT_STATEMENTS,
                                        Permissions.BRAD_DISCARD_STATEMENTS,
                                        Permissions.BRAD_RETRY_STATEMENT,
                                        Permissions.BRAD_ACCESS_TROUBLESHOOTING,
                                        Permissions.BRAD_APPROVE_RECONCILIATIONS,
                                        Permissions.BRAD_UNMATCH_RECONCILIATIONS,
                                        Permissions.BRAD_EXPORT_RECONCILIATIONS,
                                        Permissions.BRAD_UPLOAD_STATEMENTS,
                                        Permissions.BRAD_MANAGE_RECONCILIATION,
                                        Permissions.BRAD_ACCESS_STATEMENTS,
                                        Permissions.BRAD_DELETE_ACCOUNTS,
                                        Permissions.BRAD_DOWNLOAD_ACCOUNTS_CSV,
                                        Permissions.BRAD_MANAGE_ACCOUNTS,
                                        Permissions.BRAD_DOWNLOAD_CONTACTS_CSV,
                                        Permissions.BRAD_DOWNLOAD_USERS_CSV
                                ),
                                "UG", List.of(
                                        Permissions.BRAD_ACCESS_COUNTRIES,
                                        Permissions.BRAD_ACCESS_ACCOUNTS,
                                        Permissions.BRAD_EXPORT_STATEMENTS,
                                        Permissions.BRAD_DISCARD_STATEMENTS,
                                        Permissions.BRAD_RETRY_STATEMENT,
                                        Permissions.BRAD_ACCESS_TROUBLESHOOTING,
                                        Permissions.BRAD_APPROVE_RECONCILIATIONS,
                                        Permissions.BRAD_UNMATCH_RECONCILIATIONS,
                                        Permissions.BRAD_EXPORT_RECONCILIATIONS,
                                        Permissions.BRAD_UPLOAD_STATEMENTS,
                                        Permissions.BRAD_MANAGE_RECONCILIATION,
                                        Permissions.BRAD_ACCESS_STATEMENTS,
                                        Permissions.BRAD_DELETE_ACCOUNTS,
                                        Permissions.BRAD_DOWNLOAD_ACCOUNTS_CSV,
                                        Permissions.BRAD_MANAGE_ACCOUNTS,
                                        Permissions.BRAD_DOWNLOAD_CONTACTS_CSV,
                                        Permissions.BRAD_DOWNLOAD_USERS_CSV
                                ),
                                "KE", List.of(
                                        Permissions.BRAD_ACCESS_COUNTRIES,
                                        Permissions.BRAD_ACCESS_ACCOUNTS,
                                        Permissions.BRAD_EXPORT_STATEMENTS,
                                        Permissions.BRAD_DISCARD_STATEMENTS,
                                        Permissions.BRAD_RETRY_STATEMENT,
                                        Permissions.BRAD_ACCESS_TROUBLESHOOTING,
                                        Permissions.BRAD_APPROVE_RECONCILIATIONS,
                                        Permissions.BRAD_UNMATCH_RECONCILIATIONS,
                                        Permissions.BRAD_EXPORT_RECONCILIATIONS,
                                        Permissions.BRAD_UPLOAD_STATEMENTS,
                                        Permissions.BRAD_MANAGE_RECONCILIATION,
                                        Permissions.BRAD_ACCESS_STATEMENTS,
                                        Permissions.BRAD_DELETE_ACCOUNTS,
                                        Permissions.BRAD_DOWNLOAD_ACCOUNTS_CSV,
                                        Permissions.BRAD_MANAGE_ACCOUNTS,
                                        Permissions.BRAD_DOWNLOAD_CONTACTS_CSV,
                                        Permissions.BRAD_DOWNLOAD_USERS_CSV
                                )
                        )
                )
        );

    }
}
